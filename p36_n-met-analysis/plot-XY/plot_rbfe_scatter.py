#!/usr/bin/env python3
"""
绘制RBFE散点图：GIPR vs GLP1R
包含误差棒和氨基酸标签
"""

import pandas as pd
import matplotlib.pyplot as plt
import numpy as np
import re

def read_sequence_file(filename):
    """从seq.txt文件中读取氨基酸序列"""
    with open(filename, 'r') as f:
        content = f.read()
    
    # 使用正则表达式提取氨基酸序列
    pattern = r"'([A-Z]{3})'"
    amino_acids = re.findall(pattern, content)
    return amino_acids

def create_labels(amino_acids, residue_ids):
    """创建氨基酸标签，格式为 TYR1, ALA2 等"""
    labels = []
    for i, residue_id in enumerate(residue_ids):
        if i < len(amino_acids):
            label = f"{amino_acids[i]}{residue_id}"
            labels.append(label)
        else:
            labels.append(f"UNK{residue_id}")  # 如果氨基酸序列不够长
    return labels

def main():
    # 读取数据文件
    gipr_data = pd.read_csv('rbfe_analysis_results-GIPR.csv')
    glp1r_data = pd.read_csv('rbfe_analysis_results-GLP1R.csv')
    
    # 读取氨基酸序列
    amino_acids = read_sequence_file('seq.txt')
    
    # 确保两个数据集有相同的残基ID
    common_residues = set(gipr_data['Residue_ID']).intersection(set(glp1r_data['Residue_ID']))
    
    # 过滤数据，只保留共同的残基
    gipr_filtered = gipr_data[gipr_data['Residue_ID'].isin(common_residues)].sort_values('Residue_ID')
    glp1r_filtered = glp1r_data[glp1r_data['Residue_ID'].isin(common_residues)].sort_values('Residue_ID')
    
    # 提取数据
    x_data = glp1r_filtered['RBFE_kcal_mol'].values  # GLP1R数据作为X轴
    y_data = gipr_filtered['RBFE_kcal_mol'].values   # GIPR数据作为Y轴
    x_errors = glp1r_filtered['Error_Bar_kcal_mol'].values
    y_errors = gipr_filtered['Error_Bar_kcal_mol'].values
    residue_ids = gipr_filtered['Residue_ID'].values
    
    # 创建标签
    labels = create_labels(amino_acids, residue_ids)
    
    # 创建图形
    plt.figure(figsize=(12, 10))
    
    # 绘制散点图和误差棒
    plt.errorbar(x_data, y_data, xerr=x_errors, yerr=y_errors, 
                fmt='o', markersize=8, capsize=5, capthick=2, 
                elinewidth=2, alpha=0.7, color='blue')
    
    # 添加标签
    for i, (x, y, label) in enumerate(zip(x_data, y_data, labels)):
        plt.annotate(label, (x, y), xytext=(5, 5), textcoords='offset points',
                    fontsize=10, ha='left', va='bottom',
                    bbox=dict(boxstyle='round,pad=0.3', facecolor='yellow', alpha=0.7))
    
    # 添加坐标轴参考线 (x=0 和 y=0)
    plt.axhline(y=0, color='black', linestyle='-', linewidth=1, alpha=0.8)
    plt.axvline(x=0, color='black', linestyle='-', linewidth=1, alpha=0.8)
    
    # 设置图形属性
    plt.xlabel('GLP1R RBFE (kcal/mol)', fontsize=14, fontweight='bold')
    plt.ylabel('GIPR RBFE (kcal/mol)', fontsize=14, fontweight='bold')
    plt.title('RBFE for N-Methylation in CTP-005-0036', fontsize=16, fontweight='bold')
    plt.grid(True, alpha=0.3)
    
    # 设置固定的坐标轴范围和刻度间隔
    x_min, x_max = -2.5, 17.5
    y_min, y_max = -2.5, 17.5
    tick_interval = 2  # 刻度间隔为2

    # 设置坐标轴范围
    plt.xlim(x_min, x_max)
    plt.ylim(y_min, y_max)

    # 设置刻度
    x_ticks = np.arange(x_min, x_max + tick_interval, tick_interval)
    y_ticks = np.arange(y_min, y_max + tick_interval, tick_interval)
    plt.xticks(x_ticks)
    plt.yticks(y_ticks)
    

    
    # 调整布局
    plt.tight_layout()
    
    # 保存图形
    plt.savefig('rbfe_scatter_plot.png', dpi=300, bbox_inches='tight')
    plt.savefig('rbfe_scatter_plot.pdf', bbox_inches='tight')
    
    # 显示图形
    plt.show()
    
    # 打印一些统计信息
    print(f"数据点数量: {len(x_data)}")
    print(f"GLP1R RBFE 范围: {min(x_data):.2f} 到 {max(x_data):.2f} kcal/mol")
    print(f"GIPR RBFE 范围: {min(y_data):.2f} 到 {max(y_data):.2f} kcal/mol")

if __name__ == "__main__":
    main()
